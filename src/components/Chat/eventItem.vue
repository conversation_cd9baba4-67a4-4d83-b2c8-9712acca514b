<template>
  <div class="event-item" @click="handleClick">
    <div class="event-header">
      <div class="event-time">{{ formatTime(eventData.timestamp) }}</div>
    </div>

    <div class="event-content">
      <h3 class="event-description">{{ eventData.description_text }}</h3>
    </div>
  </div>
</template>

<script setup lang="ts">


// 定义事件数据接口
interface IEvent {
  event_id: string;
  user_id: string;
  description_text: string;
  timestamp: string;
  participants: string[];
  location: string;
  topics: string[];
}

// 定义props
const props = defineProps<{
  eventData: IEvent;
}>();

// 定义emits
const emit = defineEmits<{
  click: [event: IEvent];
}>();

// 处理点击事件
const handleClick = () => {
  emit('click', props.eventData);
};

// 格式化时间 - 显示相对时间
const formatTime = (timestamp: string) => {
  const eventDate = new Date(timestamp);
  const today = new Date();

  // 重置时间到当天的开始，用于日期比较
  const eventDateOnly = new Date(eventDate.getFullYear(), eventDate.getMonth(), eventDate.getDate());
  const todayOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());

  // 计算天数差
  const diffTime = eventDateOnly.getTime() - todayOnly.getTime();
  const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));

  // 根据天数差返回相应的显示文本
  switch (diffDays) {
    case -2:
      return '前天';
    case -1:
      return '昨天';
    case 0:
      return '今天';
    case 1:
      return '明天';
    case 2:
      return '后天';
    default:
      // 其他日期显示具体日期
      return eventDate.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
  }
};


</script>

<style lang="scss" scoped>
.event-item {
  width: 220px;
  height: 220px;
  background: rgba(0, 188, 212, 0.05);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 12px;
  padding: 16px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 3px solid #00ffff;
  box-shadow: -3px 0 6px rgba(0, 255, 255, 0.3);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    background: rgba(0, 188, 212, 0.1);
    transform: translateY(-2px);
    box-shadow:
      -3px 0 6px rgba(0, 255, 255, 0.5),
      0 6px 20px rgba(0, 0, 0, 0.3);
  }
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.event-time {
  color: rgba(255, 255, 255, 0.8);
  font-size: 18px;
  font-weight: 500;
}



.event-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.event-description {
  color: #ffffff;
  font-size: 22px;
  font-weight: 700;
  line-height: 1.3;
  margin: 0 0 12px 0;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
}

.event-details {
  display: flex;
  flex-direction: column;
  gap: 6px;
  align-items: center;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.detail-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 18px;
  font-weight: 500;
}

.detail-value {
  color: rgba(255, 255, 255, 0.9);
  font-size: 19px;
}
</style>
