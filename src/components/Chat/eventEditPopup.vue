<template>
  <div v-if="show" class="event-edit-popup">
    <div class="event-edit-mask"></div>
    <div class="event-edit-content">
      <div class="event-edit-header">
        <h2 class="event-edit-title">修改事件</h2>
        <button class="close-btn" @click="handleClose">
          <img src="@/assets/icon/close.png" alt="关闭" class="close-icon" />
        </button>
      </div>

      <div class="event-edit-form">
        <!-- 事件描述 -->
        <div class="form-group">
          <label class="form-label">事件描述</label>
          <textarea
            v-model="formData.description_text"
            class="form-textarea"
            :class="{ error: descriptionError }"
            placeholder="请输入事件描述"
            rows="3"
            @input="onFormChange"
          ></textarea>
          <div v-if="descriptionError" class="error-message">请输入事件描述</div>
        </div>

        <!-- 参与者 -->
        <div class="form-group">
          <label class="form-label">参与者</label>
          <div class="participants-input">
            <div class="participant-tags">
              <span v-for="(participant, index) in displayParticipants" :key="index" class="participant-tag">
                {{ participant }}
                <button class="remove-tag-btn" @click="removeParticipant(index)">×</button>
              </span>
            </div>
            <div class="participant-input-container">
              <input
                v-model="newParticipant"
                class="form-input"
                placeholder="请输入参与者姓名"
                @keydown.enter.prevent="addParticipant"
                @input="handleParticipantInput"
                @focus="showParticipantSuggestions = true"
                @blur="hideParticipantSuggestions"
              />
            </div>
          </div>
        </div>

        <!-- 地点 -->
        <div class="form-group">
          <label class="form-label">地点</label>
          <input v-model="formData.location" class="form-input" placeholder="请输入地点" @input="onFormChange" />
        </div>

        <!-- 主题 -->
        <div class="form-group">
          <label class="form-label">主题</label>
          <div class="topics-input">
            <div class="topic-tags">
              <span v-for="(topic, index) in formData.topics" :key="index" class="topic-tag">
                {{ topic }}
                <button class="remove-tag-btn" @click="removeTopic(index)">×</button>
              </span>
            </div>
            <input
              v-model="newTopic"
              class="form-input"
              placeholder="输入主题后按回车添加"
              @keydown.enter.prevent="addTopic"
            />
          </div>
        </div>


      </div>

      <!-- 底部操作按钮 -->
      <div class="dialog-footer">
        <button class="cancel-btn" :disabled="saving" @click="handleCancel">取消</button>
        <button class="confirm-btn" :disabled="saving || !isFormValid" @click="handleConfirmSave">
          <span v-if="saving">保存中...</span>
          <span v-else>确认修改</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { showSuccessToast, showFailToast } from 'vant';
import type { IEvent, IUpdateEventRequest } from '@/apis/memory';
import { updateEvent } from '@/apis/memory';
import type { IPersonData } from '@/apis/relation';

// Props定义
interface IProps {
  show: boolean;
  eventData: IEvent | null;
  personsData?: IPersonData[];
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  close: [];
  save: [event: IEvent];
  update: [event: IEvent]; // 新增：仅更新数据，不关闭弹窗
}>();

// 表单数据
const formData = ref<IEvent>({
  event_id: '',
  user_id: '',
  description_text: '',
  timestamp: '',
  participants: [],
  location: '',
  topics: [],
});

// 新参与者和主题的输入
const newParticipant = ref('');
const newTopic = ref('');

// 参与者建议相关状态
const showParticipantSuggestions = ref(false);

// 表单验证错误状态
const descriptionError = ref(false);

// 保存加载状态
const saving = ref(false);

// 表单验证 - 检查必填字段
const isFormValid = computed(() => {
  return formData.value.description_text.trim().length > 0;
});

// 过滤的人员列表（用于建议）
const filteredPersons = computed(() => {
  if (!props.personsData || !newParticipant.value.trim()) {
    return props.personsData || [];
  }

  const searchTerm = newParticipant.value.toLowerCase();
  return props.personsData.filter(
    (person) =>
      person.canonical_name.toLowerCase().includes(searchTerm) &&
      !formData.value.participants.includes(person.person_id),
  );
});

// 创建 person_id 到人名的映射
const personIdToNameMap = computed(() => {
  const map = new Map<string, string>();
  if (props.personsData) {
    props.personsData.forEach((person) => {
      map.set(person.person_id, person.canonical_name);
    });
  }
  return map;
});

// 将 person_id 转换为人名显示
const getPersonNameById = (personId: string): string => {
  return personIdToNameMap.value.get(personId) || personId;
};

// 显示用的参与者列表（转换为人名）
const displayParticipants = computed(() => {
  return formData.value.participants.map((personId) => getPersonNameById(personId));
});

// 创建人名到 person_id 的反向映射
const nameToPersonIdMap = computed(() => {
  const map = new Map<string, string>();
  if (props.personsData) {
    props.personsData.forEach((person) => {
      map.set(person.canonical_name, person.person_id);
    });
  }
  return map;
});

// 根据人名获取 person_id
const getPersonIdByName = (name: string): string => {
  return nameToPersonIdMap.value.get(name) || name;
};

// 监听事件数据变化，初始化表单
watch(
  () => props.eventData,
  (newEventData) => {
    if (newEventData) {
      formData.value = {
        ...newEventData,
        participants: [...newEventData.participants],
        topics: [...newEventData.topics],
      };
    }
  },
  { immediate: true },
);

// 添加参与者
const addParticipant = () => {
  const inputName = newParticipant.value.trim();
  if (!inputName) return;

  // 尝试根据人名找到对应的 person_id
  const personId = getPersonIdByName(inputName);

  // 检查是否已经存在（避免重复添加）
  if (!formData.value.participants.includes(personId)) {
    formData.value.participants.push(personId);
    newParticipant.value = '';
    // 触发自动提交
    onFormChange();
  }
};

// 移除参与者
const removeParticipant = (index: number) => {
  formData.value.participants.splice(index, 1);
  // 触发自动提交
  onFormChange();
};

// 添加主题
const addTopic = () => {
  if (newTopic.value.trim() && !formData.value.topics.includes(newTopic.value.trim())) {
    formData.value.topics.push(newTopic.value.trim());
    newTopic.value = '';
    // 触发自动提交
    onFormChange();
  }
};

// 移除主题
const removeTopic = (index: number) => {
  formData.value.topics.splice(index, 1);
  // 触发自动提交
  onFormChange();
};

// 处理取消 - 直接关闭弹窗，不保存
const handleCancel = () => {
  // 重置状态
  saving.value = false;
  descriptionError.value = false;
  emit('close');
};

// 处理关闭按钮 - 直接关闭弹窗，不保存
const handleClose = () => {
  handleCancel();
};

// 处理确认保存
const handleConfirmSave = async () => {
  await handleSave();
};

// 处理参与者输入
const handleParticipantInput = () => {
  // 输入时显示建议
  showParticipantSuggestions.value = true;
};

// 隐藏参与者建议
const hideParticipantSuggestions = () => {
  // 延迟隐藏，以便点击建议项能够正常工作
  setTimeout(() => {
    showParticipantSuggestions.value = false;
  }, 200);
};

// 选择参与者
const selectParticipant = (personName: string) => {
  newParticipant.value = personName;
  addParticipant();
  showParticipantSuggestions.value = false;
};

// 表单内容更改时的处理函数
const onFormChange = () => {
  // 重置错误状态
  descriptionError.value = false;

  // 不再自动提交，只重置错误状态
  // 表单将在弹窗关闭时提交
};

// 处理保存
const handleSave = async () => {
  // 重置错误状态
  descriptionError.value = false;

  // 验证必填字段
  if (!formData.value.description_text.trim()) {
    descriptionError.value = true;
    return;
  }

  // 设置保存状态
  saving.value = true;

  try {
    // 构建更新请求参数
    const updateParams: IUpdateEventRequest = {
      user_id: formData.value.user_id,
      event_id: formData.value.event_id,
      description_text: formData.value.description_text,
      participants: formData.value.participants,
      location: formData.value.location,
      topics: formData.value.topics,
    };

    console.log('📤 [eventEditPopup.vue] 开始更新事件:', updateParams);

    // 调用更新事件API
    const response = await updateEvent(updateParams);

    console.log('📡 [eventEditPopup.vue] 更新事件响应:', response);

    if (response && response.success) {
      console.log('✅ [eventEditPopup.vue] 事件更新成功');

      // 显示成功提示
      showSuccessToast({
        message: '事件更新成功了喔～',
        duration: 2000,
        icon: 'success',
        zIndex: 10002, // 确保在弹窗之上显示
      });

      // 发送save事件通知父组件更新数据并关闭弹窗
      emit('save', { ...formData.value });

      // 关闭弹窗
      emit('close');
    } else {
      console.warn('⚠️ [eventEditPopup.vue] 自动更新事件失败:', response);
      showFailToast({
        message: '更新事件失败',
        duration: 3000,
        zIndex: 10002, // 确保在弹窗之上显示
      });
    }
  } catch (error) {
    console.error('❌ [eventEditPopup.vue] 自动更新事件失败:', error);
    showFailToast({
      message: '更新事件失败',
      duration: 3000,
      zIndex: 10002, // 确保在弹窗之上显示
    });
  } finally {
    saving.value = false;
  }
};
</script>

<style lang="scss" scoped>
.event-edit-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  animation: fadeIn 0.3s ease-out;
}

.event-edit-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
}

.event-edit-content {
  background: rgba(1, 28, 32, 0.6);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  transition: all 0.3s ease;
  width: 90%;
  max-width: 600px;
  height: 800px;
  overflow-y: auto;
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.3s ease-out;
}

.event-edit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
  padding-bottom: 18px;
  position: relative;
  height: 80px;
}

.event-edit-title {
  color: rgba(255, 255, 255, 0.9);
  font-size: 40px; // 增加8px (原来32px)
  font-weight: 600;
  margin: 0;
  flex: 1;
}

.close-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: scale(1.1);
  }

  .close-icon {
    width: 24px;
    height: 24px;
    filter: brightness(0) invert(1);
  }
}

.event-edit-form {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
  padding-right: 4px;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  border: none;
  border-radius: 16px;
  padding: 20px;
  background: rgba(0, 188, 212, 0.05);
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
}

.form-label {
  color: white;
  font-size: 30px; // 增加8px (原来22px)
  font-weight: 600;
  margin-bottom: 8px;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  font-size: 28px; // 增加8px (原来20px)
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.95);
  transition: all 0.2s ease;
  box-sizing: border-box;

  &:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
  }

  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }
}

.form-textarea {
  resize: vertical;
  min-height: 80px;

  &.error {
    border-color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
  }
}

.error-message {
  color: #ef4444;
  font-size: 22px; // 增加8px (原来14px)
  margin-top: 4px;
}

.participants-input,
.topics-input {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.participant-input-container {
  position: relative;
}

.participant-tags,
.topic-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.participant-tag,
.topic-tag {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: rgba(0, 188, 212, 0.02);
  color: #00bcd4;
  border: 2px solid #00bcd4;
  border-radius: 16px;
  font-size: 30px; // 增加8px (原来22px)
  font-weight: 500;
}

.remove-tag-btn {
  background: none;
  border: none;
  color: #00bcd4;
  cursor: pointer;
  font-size: 24px; // 增加8px (原来16px)
  font-weight: bold;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(0, 188, 212, 0.3);
  }
}

.dialog-footer {
  display: flex;
  gap: 20px;
  margin-top: 40px;
  padding-top: 32px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);

  .cancel-btn,
  .confirm-btn {
    flex: 1;
    padding: 16px 16px;
    border-radius: 20px;
    font-size: 36px; // 增加8px (原来28px)
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    background: transparent;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .cancel-btn {
    color: rgba(255, 255, 255, 0.8);
    border-color: rgba(255, 255, 255, 0.8);

    &:hover:not(:disabled) {
      background: rgba(255, 255, 255, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
    }
  }

  .confirm-btn {
    color: #00bcd4;
    border-color: #00bcd4;

    &:hover:not(:disabled) {
      background: rgba(0, 188, 212, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
    }
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
