/**
 * iOS Safari 兼容性修复工具
 * 解决iOS设备上的显示和交互问题
 */

// 检测iOS设备
export const isIOSDevice = (): boolean => {
  return /iPad|iPhone|iPod/.test(navigator.userAgent);
};

// 检测iOS Safari浏览器
export const isIOSSafari = (): boolean => {
  const isIOS = isIOSDevice();
  return isIOS && /Safari/.test(navigator.userAgent) && !/CriOS|FxiOS/.test(navigator.userAgent);
};

// 获取iOS版本
export const getIOSVersion = (): number | null => {
  const match = navigator.userAgent.match(/OS (\d+)_/);
  return match ? parseInt(match[1], 10) : null;
};

// 修复iOS Safari中的滚动问题
export const fixIOSScrolling = (element: HTMLElement): void => {
  if (!isIOSDevice()) return;

  // 添加iOS滚动优化
  (element.style as any).webkitOverflowScrolling = 'touch';
  element.style.transform = 'translateZ(0)';
  (element.style as any).webkitTransform = 'translateZ(0)';
};

// 修复iOS Safari中的层级问题
export const fixIOSLayering = (element: HTMLElement, zIndex = 10): void => {
  if (!isIOSDevice()) return;

  element.style.transform = 'translateZ(0)';
  element.style.webkitTransform = 'translateZ(0)';
  element.style.willChange = 'transform';
  element.style.zIndex = zIndex.toString();
};

// 修复iOS Safari中的毛玻璃效果
export const fixIOSBackdropFilter = (element: HTMLElement): void => {
  if (!isIOSDevice()) return;

  const computedStyle = window.getComputedStyle(element);
  const backdropFilter = computedStyle.backdropFilter || (computedStyle as any).webkitBackdropFilter;

  if (backdropFilter && backdropFilter !== 'none') {
    (element.style as any).webkitBackdropFilter = backdropFilter;
  }
};

// 修复iOS Safari中的视口问题
export const fixIOSViewport = (): void => {
  if (!isIOSDevice()) return;

  // 设置视口高度
  const setViewportHeight = () => {
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
  };

  setViewportHeight();
  window.addEventListener('resize', setViewportHeight);
  window.addEventListener('orientationchange', () => {
    setTimeout(setViewportHeight, 100);
  });
};

// 修复iOS Safari中的键盘问题
export const fixIOSKeyboard = (): void => {
  if (!isIOSDevice()) return;

  let originalViewportHeight = window.innerHeight;
  let keyboardHeight = 0;

  const updateKeyboardState = (isVisible: boolean, height: number = 0) => {
    keyboardHeight = height;

    if (isVisible) {
      document.body.classList.add('ios-keyboard-visible');
      // 更新CSS变量，让页面知道键盘高度
      document.documentElement.style.setProperty('--keyboard-height', `${height}px`);
      console.log('🎹 [iosCompatibility] iOS键盘弹起，键盘高度:', height);
    } else {
      document.body.classList.remove('ios-keyboard-visible');
      document.documentElement.style.setProperty('--keyboard-height', '0px');
      console.log('🎹 [iosCompatibility] iOS键盘收起');
    }
  };

  const handleViewportChange = () => {
    const currentHeight = window.innerHeight;
    const heightDiff = originalViewportHeight - currentHeight;

    // 键盘弹起的阈值调整为100px，更敏感
    if (heightDiff > 100) {
      updateKeyboardState(true, heightDiff);
    } else {
      updateKeyboardState(false, 0);
    }
  };

  // 优先使用Visual Viewport API（更准确）
  if (window.visualViewport) {
    window.visualViewport.addEventListener('resize', () => {
      const viewport = window.visualViewport;
      if (!viewport) return;

      const windowHeight = window.innerHeight;
      const viewportHeight = viewport.height;
      const heightDiff = windowHeight - viewportHeight;

      console.log('🎹 [iosCompatibility] Visual Viewport变化:', {
        windowHeight,
        viewportHeight,
        heightDiff,
        scale: viewport.scale,
      });

      // 使用更精确的键盘检测
      if (heightDiff > 100 && viewport.scale === 1) {
        updateKeyboardState(true, heightDiff);
      } else {
        updateKeyboardState(false, 0);
      }
    });

    // 监听滚动，确保键盘状态正确
    window.visualViewport.addEventListener('scroll', () => {
      const viewport = window.visualViewport;
      if (!viewport) return;

      const heightDiff = window.innerHeight - viewport.height;

      if (heightDiff > 100) {
        updateKeyboardState(true, heightDiff);
      }
    });
  } else {
    // 备用方案
    window.addEventListener('resize', handleViewportChange);
  }

  // 记录初始视口高度
  const recordInitialHeight = () => {
    originalViewportHeight = window.innerHeight;
    console.log('🎹 [iosCompatibility] 记录初始视口高度:', originalViewportHeight);
  };

  if (document.readyState === 'complete') {
    recordInitialHeight();
  } else {
    window.addEventListener('load', recordInitialHeight);
  }

  // 监听输入框焦点事件，辅助键盘检测
  document.addEventListener('focusin', (e) => {
    const target = e.target as HTMLElement;
    if (target && (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA')) {
      console.log('🎹 [iosCompatibility] 输入框获得焦点，等待键盘弹起');
      // 延迟检测键盘状态
      setTimeout(() => {
        if (window.visualViewport) {
          const heightDiff = window.innerHeight - window.visualViewport.height;
          if (heightDiff > 100) {
            updateKeyboardState(true, heightDiff);
          }
        }
      }, 300);
    }
  });

  document.addEventListener('focusout', () => {
    console.log('🎹 [iosCompatibility] 输入框失去焦点，等待键盘收起');
    // 延迟检测键盘状态
    setTimeout(() => {
      if (window.visualViewport) {
        const heightDiff = window.innerHeight - window.visualViewport.height;
        if (heightDiff <= 100) {
          updateKeyboardState(false, 0);
        }
      }
    }, 300);
  });
};

// 修复iOS Safari中的触摸问题
export const fixIOSTouch = (element: HTMLElement): void => {
  if (!isIOSDevice()) return;

  (element.style as any).webkitTapHighlightColor = 'transparent';
  (element.style as any).webkitTouchCallout = 'none';
  element.style.touchAction = 'manipulation';
};

// 综合修复函数
export const applyIOSFixes = (): void => {
  if (!isIOSDevice()) {
    console.log('🔍 [iosCompatibility] 非iOS设备，跳过iOS兼容性修复');
    return;
  }

  console.log('🔧 [iosCompatibility] 开始应用iOS兼容性修复');

  // 修复视口问题
  fixIOSViewport();

  // 修复键盘问题
  fixIOSKeyboard();

  // 为body添加iOS标识类
  document.body.classList.add('ios-device');
  
  if (isIOSSafari()) {
    document.body.classList.add('ios-safari');
  }

  console.log('✅ [iosCompatibility] iOS兼容性修复应用完成');
};

// 调试信息输出
export const logIOSDebugInfo = (): void => {
  if (!isIOSDevice()) return;

  console.log('📱 [iosCompatibility] iOS设备调试信息:', {
    isIOS: isIOSDevice(),
    isIOSSafari: isIOSSafari(),
    iosVersion: getIOSVersion(),
    userAgent: navigator.userAgent,
    viewport: {
      width: window.innerWidth,
      height: window.innerHeight,
      devicePixelRatio: window.devicePixelRatio,
    },
    visualViewport: window.visualViewport ? {
      width: window.visualViewport.width,
      height: window.visualViewport.height,
      scale: window.visualViewport.scale,
    } : null,
  });
};
