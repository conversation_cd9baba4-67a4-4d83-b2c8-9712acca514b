/**
 * iOS Safari 兼容性修复工具
 * 解决iOS设备上的显示和交互问题
 */

// 检测iOS设备
export const isIOSDevice = (): boolean => {
  return /iPad|iPhone|iPod/.test(navigator.userAgent);
};

// 检测iOS Safari浏览器
export const isIOSSafari = (): boolean => {
  const isIOS = isIOSDevice();
  return isIOS && /Safari/.test(navigator.userAgent) && !/CriOS|FxiOS/.test(navigator.userAgent);
};

// 获取iOS版本
export const getIOSVersion = (): number | null => {
  const match = navigator.userAgent.match(/OS (\d+)_/);
  return match ? parseInt(match[1], 10) : null;
};

// 修复iOS Safari中的滚动问题
export const fixIOSScrolling = (element: HTMLElement): void => {
  if (!isIOSDevice()) return;

  // 添加iOS滚动优化
  element.style.webkitOverflowScrolling = 'touch';
  element.style.transform = 'translateZ(0)';
  element.style.webkitTransform = 'translateZ(0)';
};

// 修复iOS Safari中的层级问题
export const fixIOSLayering = (element: HTMLElement, zIndex = 10): void => {
  if (!isIOSDevice()) return;

  element.style.transform = 'translateZ(0)';
  element.style.webkitTransform = 'translateZ(0)';
  element.style.willChange = 'transform';
  element.style.zIndex = zIndex.toString();
};

// 修复iOS Safari中的毛玻璃效果
export const fixIOSBackdropFilter = (element: HTMLElement): void => {
  if (!isIOSDevice()) return;

  const computedStyle = window.getComputedStyle(element);
  const backdropFilter = computedStyle.backdropFilter || computedStyle.webkitBackdropFilter;
  
  if (backdropFilter && backdropFilter !== 'none') {
    element.style.webkitBackdropFilter = backdropFilter;
  }
};

// 修复iOS Safari中的视口问题
export const fixIOSViewport = (): void => {
  if (!isIOSDevice()) return;

  // 设置视口高度
  const setViewportHeight = () => {
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
  };

  setViewportHeight();
  window.addEventListener('resize', setViewportHeight);
  window.addEventListener('orientationchange', () => {
    setTimeout(setViewportHeight, 100);
  });
};

// 修复iOS Safari中的键盘问题
export const fixIOSKeyboard = (): void => {
  if (!isIOSDevice()) return;

  let originalViewportHeight = window.innerHeight;

  const handleViewportChange = () => {
    const currentHeight = window.innerHeight;
    const heightDiff = originalViewportHeight - currentHeight;

    if (heightDiff > 150) {
      // 键盘弹起
      document.body.classList.add('ios-keyboard-visible');
      console.log('🎹 [iosCompatibility] iOS键盘弹起，高度差:', heightDiff);
    } else {
      // 键盘收起
      document.body.classList.remove('ios-keyboard-visible');
      console.log('🎹 [iosCompatibility] iOS键盘收起');
    }
  };

  // 优先使用Visual Viewport API
  if (window.visualViewport) {
    window.visualViewport.addEventListener('resize', () => {
      const viewport = window.visualViewport;
      const heightDiff = window.screen.height - viewport.height;

      if (heightDiff > 150) {
        document.body.classList.add('ios-keyboard-visible');
        console.log('🎹 [iosCompatibility] iOS键盘弹起（Visual Viewport），高度差:', heightDiff);
      } else {
        document.body.classList.remove('ios-keyboard-visible');
        console.log('🎹 [iosCompatibility] iOS键盘收起（Visual Viewport）');
      }
    });
  } else {
    // 备用方案
    window.addEventListener('resize', handleViewportChange);
  }

  // 记录初始视口高度
  window.addEventListener('load', () => {
    originalViewportHeight = window.innerHeight;
  });
};

// 修复iOS Safari中的触摸问题
export const fixIOSTouch = (element: HTMLElement): void => {
  if (!isIOSDevice()) return;

  element.style.webkitTapHighlightColor = 'transparent';
  element.style.webkitTouchCallout = 'none';
  element.style.touchAction = 'manipulation';
};

// 综合修复函数
export const applyIOSFixes = (): void => {
  if (!isIOSDevice()) {
    console.log('🔍 [iosCompatibility] 非iOS设备，跳过iOS兼容性修复');
    return;
  }

  console.log('🔧 [iosCompatibility] 开始应用iOS兼容性修复');

  // 修复视口问题
  fixIOSViewport();

  // 修复键盘问题
  fixIOSKeyboard();

  // 为body添加iOS标识类
  document.body.classList.add('ios-device');
  
  if (isIOSSafari()) {
    document.body.classList.add('ios-safari');
  }

  console.log('✅ [iosCompatibility] iOS兼容性修复应用完成');
};

// 调试信息输出
export const logIOSDebugInfo = (): void => {
  if (!isIOSDevice()) return;

  console.log('📱 [iosCompatibility] iOS设备调试信息:', {
    isIOS: isIOSDevice(),
    isIOSSafari: isIOSSafari(),
    iosVersion: getIOSVersion(),
    userAgent: navigator.userAgent,
    viewport: {
      width: window.innerWidth,
      height: window.innerHeight,
      devicePixelRatio: window.devicePixelRatio,
    },
    visualViewport: window.visualViewport ? {
      width: window.visualViewport.width,
      height: window.visualViewport.height,
      scale: window.visualViewport.scale,
    } : null,
  });
};
